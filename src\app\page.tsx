'use client';

import { useEffect, useRef, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ThemeToggle } from "@/components/theme-toggle";
import { GitHubStats } from "@/components/github-stats";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faHome, 
  faUser, 
  faLaptopCode, 
  faEnvelope,
  faArrowRight,
  faCode,
  faBars,
  faXmark,
  faHashtag,
  faLocationDot,
  faGraduationCap,
} from '@fortawesome/free-solid-svg-icons';
import {
  faGithub,
  faLinkedin,
  faInstagram
} from '@fortawesome/free-brands-svg-icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

export default function Home() {
  const sectionsRef = useRef<(HTMLElement | null)[]>([]);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [age, setAge] = useState<number>(0);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    const sections = sectionsRef.current;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          console.log('Observer entry:', entry.target.id, entry.isIntersecting); // Debug
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
            // Add animation classes to education items when section is visible
            if (entry.target.id === 'education') {
              const educationItems = entry.target.querySelectorAll('.education-item');
              educationItems.forEach((item) => {
                item.classList.add('opacity-100', 'translate-y-0');
              });
            }
          } else {
            entry.target.classList.remove('visible');
            // Reset animation classes when section is not visible
            if (entry.target.id === 'education') {
              const educationItems = entry.target.querySelectorAll('.education-item');
              educationItems.forEach((item) => {
                item.classList.remove('opacity-100', 'translate-y-0');
              });
            }
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px',
      }
    );

    sections.forEach((section) => {
      if (section) {
        observer.observe(section);
      }
    });

    return () => {
      sections.forEach((section) => {
        if (section) {
          observer.unobserve(section);
        }
      });
    };
  }, []);

  useEffect(() => {
    // Calculate age based on date of birth
    const calculateAge = () => {
      const dob = new Date('2002-08-30');
      const today = new Date();
      let age = today.getFullYear() - dob.getFullYear();
      const monthDiff = today.getMonth() - dob.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
        age--;
      }
      
      setAge(age);
    };

    calculateAge();
  }, []);

  const navLinks = [
    { href: '#hero', icon: faHome, text: 'Home' },
    { href: '#about', icon: faUser, text: 'About' },
    { href: '#education', icon: faGraduationCap, text: 'Education' },
    { href: '#skills', icon: faCode, text: 'Skills' },
    { href: '#projects', icon: faLaptopCode, text: 'Projects' },
    { href: '#contact', icon: faEnvelope, text: 'Contact' },
  ];

  const handleNavClick = () => {
    setIsMobileMenuOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formData = new FormData(e.currentTarget);
    const data = {
      Name: formData.get('Name')?.toString() || '',
      Email: formData.get('Email')?.toString() || '',
      Message: formData.get('Message')?.toString() || '',
    };

    try {
      console.log('Submitting form data:', data); // Debug log

      const response = await fetch(
        'https://script.google.com/macros/s/AKfycbzSZTAY8dgzZg7Z_zT2ycooxwePGC3yCGUJsfCv-_1GjWbuQFq8nwfaPAsGqokQ_VsC/exec',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams(data),
        }
      );

      console.log('Response status:', response.status); // Debug log
      console.log('Response ok:', response.ok); // Debug log

      // Check if response is ok first
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Response result:', result); // Debug log

      if (result.result === 'success') {
        setShowSuccessDialog(true);
        e.currentTarget.reset();
      } else {
        console.error('Form submission failed:', result);
        setShowErrorDialog(true);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setShowErrorDialog(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <main 
      className="min-h-screen relative"
      onClick={() => isMobileMenuOpen && setIsMobileMenuOpen(false)}
    >
      <div className="fixed top-4 right-4 z-50">
        <ThemeToggle />
      </div>
      
      {/* Mobile Menu Button */}
      <button
        className="fixed top-4 left-4 z-50 p-2 md:hidden"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        <FontAwesomeIcon 
          icon={isMobileMenuOpen ? faXmark : faBars} 
          className="w-6 h-6"
        />
      </button>

      {/* Mobile Sidebar Navigation */}
      <div 
        className={`
          fixed top-0 left-0 h-full w-64 bg-background/95 backdrop-blur-md border-r 
          transform transition-transform duration-300 ease-in-out z-40
          md:hidden
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        `}
        onClick={(e) => e.stopPropagation()}  // Prevent clicks inside menu from closing it
      >
        <div className="pt-20 px-4">
          {navLinks.map((link) => (
            <a
              key={link.href}
              href={link.href}
              onClick={handleNavClick}
              className="flex items-center gap-3 py-3 px-4 text-sm font-medium hover:text-primary transition-colors rounded-lg hover:bg-primary/10"
            >
              <FontAwesomeIcon icon={link.icon} className="w-4 h-4" />
              {link.text}
            </a>
          ))}
        </div>
      </div>

      {/* Desktop Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-40 bg-background/60 backdrop-blur-md border-b hidden md:block">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-16 gap-8">
            {navLinks.map((link) => (
              <a
                key={link.href}
                href={link.href}
                className="text-sm font-medium hover:text-primary transition-colors flex items-center gap-2 opacity-75 hover:opacity-100"
              >
                <FontAwesomeIcon icon={link.icon} className="w-4 h-4" />
                {link.text}
              </a>
            ))}
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="min-h-screen w-full flex items-center justify-center bg-dot-pattern">
        <div ref={(el) => { sectionsRef.current[0] = el }} className="section-content w-full max-w-3xl text-center flex flex-col items-center gap-8 px-4">
          <Avatar className="w-32 h-32 mx-auto ring-2 ring-border">
            <AvatarImage src="/images/profile.jpg" alt="Dexter Dykes Timothy" className="" />
            <AvatarFallback>DT</AvatarFallback>
          </Avatar>
          <div className="space-y-4">
            <h1 className="text-5xl font-bold tracking-tight">Dexter Dykes Timothy</h1>
            <p className="text-xl text-muted-foreground">Computer Science Nerd | Passionate Software Engineer</p>
          </div>
          <Button size="lg" className="mt-4 text-lg group" asChild>
            <a href="#contact">
              Contact Me
              <FontAwesomeIcon icon={faArrowRight} className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </a>
          </Button>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="min-h-screen w-full flex items-center justify-center">
        <div ref={(el) => { sectionsRef.current[1] = el }} className="section-content w-full max-w-5xl px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">About Me</h2>
          <Card className="border-2">
            <div className="grid grid-cols-1 md:grid-cols-2">
              {/* Left Side - Image and Personal Details */}
              <div className="p-8 border-r border-border">
                <div className="space-y-6">
                  <div className="relative w-64 h-64 mx-auto">
                    <Avatar className="w-full h-full ring-4 ring-border">
                      <AvatarImage src="/images/profile2.jpg" alt="Dexter Dykes Timothy" />
                      <AvatarFallback>DT</AvatarFallback>
                    </Avatar>
                  </div>
                  <div className="space-y-4 text-center">
                    <h3 className="text-2xl font-semibold">Dexter Dykes Timothy</h3>
                    <div className="space-y-2 text-muted-foreground">
                      <p className="flex items-center justify-center gap-2">
                        <FontAwesomeIcon icon={faHashtag} className="w-4 h-4" />
                        {age} Years Old
                      </p>
                      <p className="flex items-center justify-center gap-2">
                        <FontAwesomeIcon icon={faLocationDot} className="w-4 h-4" />
                        Based in Malaysia
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Description */}
              <div className="p-8">
                <div className="space-y-6">
                  <p className="text-muted-foreground text-lg leading-relaxed">
                    I&apos;m a Software Engineer passionate about building innovative web and mobile applications. I enjoy solving complex problems through technology and have experience with C++, Java, PHP, and Kotlin. I focus on creating user-centric, efficient solutions and thrive in dynamic, forward-thinking teams.
                  </p>
                  <div className="flex flex-wrap gap-4">
                    <Button variant="outline" className="group hover:border-primary/50" asChild>
                      <a href="https://github.com/DyDxdYdX" target="_blank" rel="noopener noreferrer">
                        <FontAwesomeIcon icon={faGithub} className="w-4 h-4 mr-2 group-hover:text-primary" />
                        GitHub
                      </a>
                    </Button>
                    <Button variant="outline" className="group hover:border-primary/50" asChild>
                      <a href="https://www.linkedin.com/in/dykesdexter" target="_blank" rel="noopener noreferrer">
                        <FontAwesomeIcon icon={faLinkedin} className="w-4 h-4 mr-2 group-hover:text-primary" />
                        LinkedIn
                      </a>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Education Section */}
      <section id="education" className="min-h-screen w-full flex items-center justify-center py-16">
        <div ref={(el) => { sectionsRef.current[2] = el }} className="section-content w-full max-w-5xl px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Education</h2>
          <div className="relative">
            {/* Timeline line (only between first and last dot) */}
            <div className="absolute left-1/2 transform -translate-x-1/2 top-8 bottom-8 w-px bg-gray-300 z-0"></div>
            {/* Timeline items */}
            <div className="space-y-12 relative z-10">
              {[
                {
                  year: "2021 - 2025",
                  title: "Bachelor of Computer Science (Hons) in Software Engineering",
                  institution: "Universiti Malaysia Sabah (UMS)",
                  location: "Kota Kinabalu, Sabah",
                  achievement: "CGPA 3.50"
                },
                {
                  year: "2020 - 2021",
                  title: "Sains-Modul II (M002)-SDS",
                  institution: "Kolej Matrikulasi Labuan",
                  location: "Labuan",
                  achievement: "CGPA 3.54"
                },
                {
                  year: "2019",
                  title: "SPM",
                  institution: "SMK Elopura",
                  location: "Sandakan, Sabah",
                  achievement: "SPM 5A"
                }
              ].map((item, index) => (
                <div key={index} className="relative">
                  {/* Timeline dot, slightly larger and overlapping card */}
                  <div className="absolute left-1/2 transform -translate-x-1/2 -top-4 w-5 h-5 rounded-full bg-primary border-4 border-white shadow z-20"></div>
                  {/* Content */}
                  <div className={`flex flex-col items-end md:items-center ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}`}>
                    <div className="w-full md:w-1/2 px-4 md:px-8">
                      <div className="bg-card p-6 rounded-lg border shadow-sm hover:border-primary/50 transition-all duration-300">
                        <div className="text-sm text-muted-foreground mb-2">{item.year}</div>
                        <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
                        <p className="text-muted-foreground mb-2">{item.institution}</p>
                        <p className="text-sm text-muted-foreground mb-2">{item.achievement}</p>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <FontAwesomeIcon icon={faLocationDot} className="w-4 h-4 mr-2" />
                          {item.location}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="min-h-screen w-full flex items-center justify-center py-16">
        <div ref={(el) => { sectionsRef.current[3] = el }} className="section-content w-full max-w-5xl px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Skills & Expertise</h2>
          <GitHubStats />
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="min-h-screen w-full flex items-center justify-center py-16">
        <div ref={(el) => { sectionsRef.current[4] = el }} className="section-content w-full max-w-5xl px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Sabah Holiday Fetcher",
                description: "A simple API that provides public holiday data for Malaysia (Sabah) in JSON format.",
                link: "https://sabah-holiday.dydxsoft.my/"
              },
              {
                title: "Shophawk",
                description: "An e-commerce platform that helps users discover and compare prices across different online stores.",
                link: "https://github.com/DyDxdYdX/shophawk"
              },{
                title: "Minigame by DyDxSoft",
                description: "Minigame website where you can play classic gane for free without annoying ads.",
                link: "https://minigame.dydxsoft.my"
              },
              {
                title: "Ongoing Projects",
                description: "And more projects coming soon!",
                link: "https://github.com/DyDxdYdX?tab=repositories"
              }
            ].map((project) => (
              <Card key={project.title} className="group hover:border-primary/50 transition-all duration-300">
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">{project.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">{project.description}</p>
                  <Button variant="ghost" className="group-hover:text-primary group-hover:bg-primary/10" asChild>
                    <a href={project.link} target="_blank" rel="noopener noreferrer">
                      View Project
                      <FontAwesomeIcon icon={faArrowRight} className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </a>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="min-h-screen w-full flex items-center justify-center">
        <div ref={(el) => { sectionsRef.current[5] = el }} className="section-content w-full max-w-5xl px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Get in Touch</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Left Side - Contact Buttons */}
            <div className="space-y-8">
              <p className="text-muted-foreground text-lg">Feel free to reach out for collaborations or just a friendly hello!</p>
              <div className="flex flex-wrap gap-4">
                <Button variant="outline" size="lg" className="group hover:border-primary/50" asChild>
                  <a href="mailto:<EMAIL>">
                    <FontAwesomeIcon icon={faEnvelope} className="w-4 h-4 mr-2 group-hover:text-primary" />
                    Email
                  </a>
                </Button>
                <Button variant="outline" size="lg" className="group hover:border-primary/50" asChild>
                  <a href="https://github.com/DyDxdYdX" target="_blank" rel="noopener noreferrer">
                    <FontAwesomeIcon icon={faGithub} className="w-4 h-4 mr-2 group-hover:text-primary" />
                    GitHub
                  </a>
                </Button>
                <Button variant="outline" size="lg" className="group hover:border-primary/50" asChild>
                  <a href="https://www.linkedin.com/in/dykesdexter" target="_blank" rel="noopener noreferrer">
                    <FontAwesomeIcon icon={faLinkedin} className="w-4 h-4 mr-2 group-hover:text-primary" />
                    LinkedIn
                  </a>
                </Button>
                <Button variant="outline" size="lg" className="group hover:border-primary/50" asChild>
                  <a href="https://www.instagram.com/dexterdykes/" target="_blank" rel="noopener noreferrer">
                    <FontAwesomeIcon icon={faInstagram} className="w-4 h-4 mr-2 group-hover:text-primary" />
                    Instagram
                  </a>
                </Button>
              </div>
            </div>

            {/* Right Side - Contact Form */}
            <div className="bg-card p-6 rounded-lg border">
              <form
                ref={formRef}
                onSubmit={handleSubmit}
                className="space-y-4"
              >
                <div className="space-y-2">
                  <label htmlFor="Name" className="text-sm font-medium">Name</label>
                  <input
                    type="text"
                    id="Name"
                    name="Name"
                    required
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
                    placeholder="Your name"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="Email" className="text-sm font-medium">Email</label>
                  <input
                    type="email"
                    id="Email"
                    name="Email"
                    required
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="Message" className="text-sm font-medium">Message</label>
                  <textarea
                    id="Message"
                    name="Message"
                    required
                    rows={4}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 resize-none"
                    placeholder="Your message..."
                  />
                </div>
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Success Alert Dialog */}
      <AlertDialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Message Sent Successfully!</AlertDialogTitle>
            <AlertDialogDescription>
              Thank you for reaching out. I'll get back to you as soon as possible.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction>Close</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Error Alert Dialog */}
      <AlertDialog open={showErrorDialog} onOpenChange={setShowErrorDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Something went wrong</AlertDialogTitle>
            <AlertDialogDescription>
              There was an error sending your message. Please try again later or contact me through one of the other channels.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction>Close</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Footer */}
      <footer className="w-full py-8 border-t">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center gap-6">
            <div className="flex items-center gap-6">
              <a href="https://github.com/DyDxdYdX" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors">
                <FontAwesomeIcon icon={faGithub} className="w-6 h-6" />
              </a>
              <a href="https://www.linkedin.com/in/dykesdexter" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors">
                <FontAwesomeIcon icon={faLinkedin} className="w-6 h-6" />
              </a>
              <a href="https://www.instagram.com/dexterdykes/" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-primary transition-colors">
                <FontAwesomeIcon icon={faInstagram} className="w-6 h-6" />
              </a>
            </div>
            <div className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} Dexter Dykes Timothy. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </main>
  );
}
